import { lazy, Suspense } from 'react';

// Lazy load React Query DevTools only in development
const ReactQueryDevtools = lazy(() =>
  import('@tanstack/react-query-devtools').then((module) => ({
    default: module.ReactQueryDevtools,
  }))
);

/**
 * React Query DevTools component
 *
 * This component renders the React Query DevTools in development mode only.
 * It's conditionally imported to avoid including it in the production bundle.
 */
export function TanStackDevTools() {
  // Only render in development mode
  if (import.meta.env.DEV) {
    return (
      <Suspense fallback={null}>
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
        />
      </Suspense>
    );
  }

  // Return null in production
  return null;
}
