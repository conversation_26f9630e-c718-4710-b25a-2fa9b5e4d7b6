/**
 * Service Worker for Performance Optimization
 * 
 * This service worker implements advanced caching strategies
 * to improve CSS loading and overall performance
 */

const CACHE_NAME = 'badhees-v1';
const STATIC_CACHE = 'badhees-static-v1';
const DYNAMIC_CACHE = 'badhees-dynamic-v1';

// Resources to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.webmanifest',
  '/icons/favicon-32x32.svg',
  '/icons/favicon-16x16.svg',
  '/icons/apple-touch-icon.svg'
];

// CSS files to cache with high priority
const CSS_ASSETS = [
  '/assets/index.css',
  '/assets/critical.css'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      // Cache CSS assets
      caches.open(DYNAMIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching CSS assets');
        return cache.addAll(CSS_ASSETS.filter(asset => asset));
      })
    ]).then(() => {
      console.log('Service Worker: Installation complete');
      return self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
            console.log('Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activation complete');
      return self.clients.claim();
    })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip external requests
  if (url.origin !== location.origin) {
    return;
  }
  
  // Handle different types of requests
  if (request.destination === 'style' || url.pathname.endsWith('.css')) {
    // CSS files - Cache First strategy
    event.respondWith(handleCSSRequest(request));
  } else if (request.destination === 'script' || url.pathname.endsWith('.js')) {
    // JavaScript files - Cache First strategy
    event.respondWith(handleJSRequest(request));
  } else if (request.destination === 'image') {
    // Images - Cache First with fallback
    event.respondWith(handleImageRequest(request));
  } else if (url.pathname === '/' || url.pathname.endsWith('.html')) {
    // HTML files - Network First strategy
    event.respondWith(handleHTMLRequest(request));
  } else {
    // Other assets - Cache First strategy
    event.respondWith(handleOtherRequest(request));
  }
});

// CSS Request Handler - Cache First
async function handleCSSRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('Service Worker: Serving CSS from cache:', request.url);
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
      console.log('Service Worker: Cached CSS from network:', request.url);
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: CSS request failed:', error);
    return new Response('/* CSS loading failed */', {
      headers: { 'Content-Type': 'text/css' }
    });
  }
}

// JavaScript Request Handler - Cache First
async function handleJSRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: JS request failed:', error);
    return new Response('console.error("Script loading failed");', {
      headers: { 'Content-Type': 'application/javascript' }
    });
  }
}

// Image Request Handler - Cache First with fallback
async function handleImageRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Image request failed:', error);
    // Return a placeholder image or empty response
    return new Response('', { status: 404 });
  }
}

// HTML Request Handler - Network First
async function handleHTMLRequest(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: HTML request failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page if available
    const offlinePage = await caches.match('/offline.html');
    return offlinePage || new Response('Offline', { status: 503 });
  }
}

// Other Request Handler - Cache First
async function handleOtherRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Request failed:', error);
    return new Response('', { status: 404 });
  }
}

// Background sync for performance metrics
self.addEventListener('sync', (event) => {
  if (event.tag === 'performance-metrics') {
    event.waitUntil(syncPerformanceMetrics());
  }
});

async function syncPerformanceMetrics() {
  try {
    // Send cached performance metrics when online
    const cache = await caches.open('performance-metrics');
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      const data = await response.json();
      
      // Send to analytics endpoint
      await fetch('/api/analytics/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      
      // Remove from cache after successful send
      await cache.delete(request);
    }
  } catch (error) {
    console.error('Service Worker: Failed to sync performance metrics:', error);
  }
}
