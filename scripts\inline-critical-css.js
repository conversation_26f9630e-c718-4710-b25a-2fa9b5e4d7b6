import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read critical CSS
const criticalCssPath = path.join(__dirname, '../src/styles/critical.css');
const criticalCss = fs.readFileSync(criticalCssPath, 'utf8');

// Minify CSS (basic minification)
const minifiedCss = criticalCss
  .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
  .replace(/\s+/g, ' ') // Replace multiple spaces with single space
  .replace(/;\s*}/g, '}') // Remove semicolon before closing brace
  .replace(/\s*{\s*/g, '{') // Remove spaces around opening brace
  .replace(/;\s*/g, ';') // Remove spaces after semicolon
  .replace(/,\s*/g, ',') // Remove spaces after comma
  .replace(/:\s*/g, ':') // Remove spaces after colon
  .trim();

// Read index.html template
const indexHtmlPath = path.join(__dirname, '../index.html');
let indexHtml = fs.readFileSync(indexHtmlPath, 'utf8');

// Create the critical CSS style tag
const criticalCssTag = `    <style id="critical-css">
${minifiedCss}
    </style>`;

// Insert critical CSS before the closing </head> tag
if (indexHtml.includes('<style id="critical-css">')) {
  // Replace existing critical CSS
  indexHtml = indexHtml.replace(
    /<style id="critical-css">[\s\S]*?<\/style>/,
    criticalCssTag
  );
} else {
  // Insert new critical CSS
  indexHtml = indexHtml.replace(
    '</head>',
    `${criticalCssTag}
  </head>`
  );
}

// Write back to index.html
fs.writeFileSync(indexHtmlPath, indexHtml);

console.log('✅ Critical CSS inlined successfully!');
console.log(`📦 Critical CSS size: ${(minifiedCss.length / 1024).toFixed(2)}KB`);
