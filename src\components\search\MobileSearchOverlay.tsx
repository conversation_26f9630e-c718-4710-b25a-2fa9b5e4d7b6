import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, ArrowLeft, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SearchInput } from './SearchInput';
import { SearchResults } from './SearchResults';
import { SearchSuggestions } from './SearchSuggestions';
import { FrontendProduct } from '@/services/product/types';
import {
  searchProducts,
  getSearchSuggestions,
  getPopularSearches,
  getUserRecentSearches
} from '@/services/search';
import { cn } from '@/lib/utils';
import './search-overlay.css';

export interface MobileSearchOverlayProps {
  /**
   * Whether the overlay is open
   */
  isOpen: boolean;

  /**
   * Callback when the overlay is closed
   */
  onClose: () => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A full-screen search overlay optimized for mobile devices
 */
export const MobileSearchOverlay: React.FC<MobileSearchOverlayProps> = ({
  isOpen,
  onClose,
  className = "",
}) => {
  const navigate = useNavigate();
  const overlayRef = useRef<HTMLDivElement>(null);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<FrontendProduct[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // State for suggestions
  const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<{ query: string; count: number }[]>([]);
  const [recentSearches, setRecentSearches] = useState<{ query: string; timestamp: Date }[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  // Simple search handler without complex state management
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchProducts(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching products:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle result selection
  const handleResultSelect = (product: FrontendProduct) => {
    navigate(`/products/${product.id}`);
    onClose();
  };

  // Handle view all results
  const handleViewAllResults = () => {
    navigate(`/products?search=${encodeURIComponent(searchQuery)}`);
    onClose();
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (query: string) => {
    setSearchQuery(query);
    handleSearch(query);
  };

  // Debounced search effect
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    if (searchQuery.length >= 2) {
      const timeoutId = setTimeout(() => {
        handleSearch(searchQuery);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery]);

  // Load suggestions when the overlay opens
  useEffect(() => {
    if (isOpen) {
      const loadSuggestions = async () => {
        setIsLoadingSuggestions(true);
        try {
          // Load popular searches
          const popular = await getPopularSearches(5);
          setPopularSearches(popular);

          // Load recent searches
          const recent = await getUserRecentSearches(5);
          setRecentSearches(recent);
        } catch (error) {
          console.error('Error loading suggestions:', error);
        } finally {
          setIsLoadingSuggestions(false);
        }
      };

      loadSuggestions();
    }
  }, [isOpen]);

  // Load auto-complete suggestions when the search query changes
  useEffect(() => {
    const loadAutoCompleteSuggestions = async () => {
      if (searchQuery.trim().length < 2) {
        setAutoCompleteSuggestions([]);
        return;
      }

      try {
        const suggestions = await getSearchSuggestions(searchQuery);
        setAutoCompleteSuggestions(suggestions);
      } catch (error) {
        console.error('Error loading auto-complete suggestions:', error);
      }
    };

    loadAutoCompleteSuggestions();
  }, [searchQuery]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose]);

  // If not open, don't render anything
  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={cn(
        "fixed inset-0 z-[9999] bg-white flex flex-col search-overlay",
        className
      )}
      ref={overlayRef}
    >
      {/* Header */}
      <div className="search-overlay-header">
        <button
          type="button"
          onClick={onClose}
          className="search-header-button mr-2"
          aria-label="Back"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>

        <div className="flex-1 relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleSearch(searchQuery);
              }
            }}
            placeholder="Search for products..."
            className="w-full h-11 pl-10 pr-10 border border-gray-200 rounded-full text-base bg-white focus:outline-none focus:border-gray-400"
            autoFocus={true}
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          {searchQuery && (
            <button
              type="button"
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 hover:text-gray-600"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        <button
          type="button"
          onClick={onClose}
          className="search-header-button ml-2"
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto search-overlay-content">
        {/* Show suggestions if no search results */}
        {searchResults.length === 0 && !isSearching && (
          <SearchSuggestions
            popularSearches={popularSearches}
            recentSearches={recentSearches}
            autoCompleteSuggestions={autoCompleteSuggestions}
            onSuggestionSelect={handleSuggestionSelect}
            currentQuery={searchQuery}
            className="search-results-container"
          />
        )}

        {/* Show search results */}
        {(searchResults.length > 0 || isSearching) && (
          <SearchResults
            results={searchResults}
            query={searchQuery}
            isLoading={isSearching}
            onResultSelect={handleResultSelect}
            onViewAllResults={handleViewAllResults}
            className="search-results-container"
            maxResults={10}
          />
        )}
      </div>
    </div>
  );
};
