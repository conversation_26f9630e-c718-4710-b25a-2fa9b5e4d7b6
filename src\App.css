
/* Base Styles */
#root {
  max-width: 100%;
  margin: 0 auto;
  text-align: left;
}

/* Responsive Typography */
@media (max-width: 640px) {
  h1 {
    font-size: 1.75rem; /* 28px */
  }
  h2 {
    font-size: 1.5rem; /* 24px */
  }
  h3 {
    font-size: 1.25rem; /* 20px */
  }
  p {
    font-size: 0.9375rem; /* 15px */
  }
}

/* Animation Keyframes */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

/* Responsive Spacing */
.responsive-padding {
  padding: 1rem;
}
@media (min-width: 640px) {
  .responsive-padding {
    padding: 1.5rem;
  }
}
@media (min-width: 768px) {
  .responsive-padding {
    padding: 2rem;
  }
}
@media (min-width: 1024px) {
  .responsive-padding {
    padding: 3rem;
  }
}

/* Touch-friendly Interactions */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Utility Classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.text-gradient {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #4f46e5, #7c3aed);
}

/* Product Card Styles */
.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.stock-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
}

.stock-badge.in-stock {
  background-color: #F2FCE2;
  color: #16a34a;
}

.stock-badge.out-of-stock {
  background-color: #FEF2F2;
  color: #dc2626;
}

.stock-badge.low-stock {
  background-color: #FFFBEB;
  color: #d97706;
}

/* Image Zoom Styles */
.zoom-container {
  cursor: zoom-in;
  overflow: hidden;
  position: relative;
}

.zoom-image {
  transition: transform 0.3s ease;
}

.zoom-controls {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

/* Mobile-first Styles */
.mobile-friendly-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  width: 100%;
  border-radius: 0.5rem;
}
@media (min-width: 768px) {
  .mobile-friendly-button {
    width: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.card-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}
@media (min-width: 640px) {
  .card-container {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .card-container {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1280px) {
  .card-container {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Mobile Navigation Styles */
.mobile-nav-visible {
  padding-bottom: 4rem; /* Space for mobile nav */
}
@media (min-width: 768px) {
  .mobile-nav-visible {
    padding-bottom: 0;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Responsive Image Containers */
.aspect-square {
  aspect-ratio: 1/1;
  position: relative;
}

.aspect-video {
  aspect-ratio: 16/9;
  position: relative;
}

.responsive-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}
