/**
 * Performance Monitoring for CSS Loading
 * 
 * This module monitors and reports on CSS loading performance
 * to track render-blocking optimizations
 */

interface PerformanceMetrics {
  firstPaint: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  cssLoadTime: number;
  criticalCSSSize: number;
  renderBlockingResources: number;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observer: PerformanceObserver | null = null;

  constructor() {
    this.initializeMonitoring();
  }

  private initializeMonitoring(): void {
    // Monitor paint metrics
    this.observePaintMetrics();
    
    // Monitor layout shift
    this.observeLayoutShift();
    
    // Monitor first input delay
    this.observeFirstInputDelay();
    
    // Monitor CSS loading
    this.monitorCSSLoading();
    
    // Report metrics when page is fully loaded
    window.addEventListener('load', () => {
      setTimeout(() => this.reportMetrics(), 1000);
    });
  }

  private observePaintMetrics(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          switch (entry.name) {
            case 'first-paint':
              this.metrics.firstPaint = entry.startTime;
              break;
            case 'first-contentful-paint':
              this.metrics.firstContentfulPaint = entry.startTime;
              break;
          }
        }
      });
      
      this.observer.observe({ entryTypes: ['paint'] });
    }
  }

  private observeLayoutShift(): void {
    if ('PerformanceObserver' in window) {
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  private observeFirstInputDelay(): void {
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
        }
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
    }
  }

  private monitorCSSLoading(): void {
    const startTime = performance.now();
    let cssFilesLoaded = 0;
    let totalCSSFiles = 0;
    
    // Count CSS files
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    totalCSSFiles = links.length;
    
    // Monitor each CSS file
    links.forEach((link) => {
      if (link instanceof HTMLLinkElement) {
        link.addEventListener('load', () => {
          cssFilesLoaded++;
          if (cssFilesLoaded === totalCSSFiles) {
            this.metrics.cssLoadTime = performance.now() - startTime;
          }
        });
      }
    });
    
    // Monitor critical CSS size
    const criticalCSS = document.getElementById('critical-css');
    if (criticalCSS) {
      this.metrics.criticalCSSSize = criticalCSS.textContent?.length || 0;
    }
    
    // Count render-blocking resources
    this.metrics.renderBlockingResources = this.countRenderBlockingResources();
  }

  private countRenderBlockingResources(): number {
    let count = 0;
    
    // Count blocking stylesheets
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]:not([media="print"])');
    count += stylesheets.length;
    
    // Count blocking scripts
    const scripts = document.querySelectorAll('script:not([async]):not([defer])');
    count += scripts.length;
    
    return count;
  }

  private reportMetrics(): void {
    // Get LCP from PerformanceObserver
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
        
        // Report all metrics
        this.sendMetrics();
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } else {
      this.sendMetrics();
    }
  }

  private sendMetrics(): void {
    const report = {
      ...this.metrics,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: this.getConnectionInfo()
    };

    // Log metrics in development
    if (import.meta.env.DEV) {
      console.group('🚀 Performance Metrics');
      console.log('First Paint:', this.formatTime(report.firstPaint));
      console.log('First Contentful Paint:', this.formatTime(report.firstContentfulPaint));
      console.log('Largest Contentful Paint:', this.formatTime(report.largestContentfulPaint));
      console.log('Cumulative Layout Shift:', report.cumulativeLayoutShift?.toFixed(3));
      console.log('First Input Delay:', this.formatTime(report.firstInputDelay));
      console.log('CSS Load Time:', this.formatTime(report.cssLoadTime));
      console.log('Critical CSS Size:', this.formatBytes(report.criticalCSSSize));
      console.log('Render Blocking Resources:', report.renderBlockingResources);
      console.groupEnd();
    }

    // Send to analytics in production
    if (import.meta.env.PROD) {
      this.sendToAnalytics(report);
    }
  }

  private getConnectionInfo(): any {
    const connection = (navigator as any).connection;
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      };
    }
    return null;
  }

  private formatTime(time?: number): string {
    if (time === undefined) return 'N/A';
    return `${time.toFixed(2)}ms`;
  }

  private formatBytes(bytes?: number): string {
    if (bytes === undefined) return 'N/A';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private sendToAnalytics(report: any): void {
    // Send to your analytics service
    // Example: Google Analytics, custom endpoint, etc.
    try {
      // Example implementation
      if ('gtag' in window) {
        (window as any).gtag('event', 'performance_metrics', {
          custom_map: {
            first_paint: report.firstPaint,
            first_contentful_paint: report.firstContentfulPaint,
            largest_contentful_paint: report.largestContentfulPaint,
            cumulative_layout_shift: report.cumulativeLayoutShift,
            css_load_time: report.cssLoadTime
          }
        });
      }
    } catch (error) {
      console.warn('Failed to send performance metrics:', error);
    }
  }

  // Public method to get current metrics
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // Public method to mark custom performance events
  public mark(name: string): void {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  // Public method to measure between marks
  public measure(name: string, startMark: string, endMark?: string): void {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        console.warn('Failed to measure performance:', error);
      }
    }
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export for manual usage
export { PerformanceMonitor };
