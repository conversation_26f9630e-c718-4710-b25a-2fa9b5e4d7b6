
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { ArrowR<PERSON>, Loader2 } from "lucide-react";
import ProductCard from "../products/ProductCard";
import { Product, getInitialProducts } from "@/services/editorProductsService";
import '@/styles/animations.css';

const FeaturedProducts = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get products from Supabase
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const products = await getInitialProducts();

        // Filter for active and featured products and limit to 6 for display
        const activeProducts = products
          .filter(product => product.status === "active" && product.isFeatured)
          .slice(0, 6);

        setFeaturedProducts(activeProducts);
      } catch (error) {
        console.error('Error fetching featured products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Enhanced featured products with stock status and specifications if needed
  const enhancedProducts = featuredProducts.map(product => ({
    ...product,
    stockStatus: product.stock > 0 ? "in_stock" : "out_of_stock" as "in_stock" | "out_of_stock" | "low_stock",
    // Use real ratings from database, don't override them
    description: product.description || "Premium quality furniture piece, crafted with attention to detail and designed for comfort.",
    // Add specifications if they don't exist
    specifications: product.specifications || {
      "Dimensions": "Various sizes available",
      "Material": "Premium quality materials",
      "Care": "Refer to product manual for care instructions",
      "Origin": "Designed and crafted with attention to detail"
    }
  }));

  return (
    <section className="py-16 md:py-24">
      <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-end mb-12">
          <div className="max-w-2xl">
            <h2 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-4">
              Our Featured Collection
            </h2>
            <p className="text-lg text-badhees-600">
              Discover our most sought-after pieces, carefully curated for those who appreciate exceptional design and quality craftsmanship.
            </p>
          </div>
          <Link
            to="/products"
            className="group flex items-center text-badhees-600 hover:text-badhees-accent mt-6 md:mt-0 transition-colors"
          >
            <span className="font-medium">View All Products</span>
            <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
          </Link>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
            <span className="ml-2 text-badhees-600">Loading featured products...</span>
          </div>
        ) : enhancedProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-10">
            {enhancedProducts.map((product, index) => (
              <div
                key={product.id}
                className={`opacity-0 animate-fadeIn animation-fill-forwards animation-delay-${Math.min(Math.floor(index * 150 / 150) * 150, 1500)}`}
              >
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="text-badhees-400 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
              </svg>
            </div>
            <h3 className="text-xl font-medium text-badhees-700 mb-2">No Featured Products</h3>
            <p className="text-badhees-500 max-w-md mb-6">Our featured collection is being curated. Check back soon or explore all our products.</p>
            <Link
              to="/products"
              className="px-4 py-2 bg-badhees-accent text-white rounded-md hover:bg-badhees-accent/90 transition-colors"
            >
              View All Products
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedProducts;
