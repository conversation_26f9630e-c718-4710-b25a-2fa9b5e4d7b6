/* Critical CSS - Above the fold styles */
/* This CSS is inlined in the HTML head for immediate rendering */

/* CSS Variables - Essential for theme */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
  
  /* Badhees brand colors */
  --badhees-50: #fdf8f6;
  --badhees-100: #f2e8e5;
  --badhees-200: #eaddd7;
  --badhees-300: #e0cec7;
  --badhees-400: #d2bab0;
  --badhees-500: #bfa094;
  --badhees-600: #a18072;
  --badhees-700: #8b6f5b;
  --badhees-800: #73624f;
  --badhees-900: #5d4f42;
  --badhees-accent: #8b4513;
}

/* Base HTML elements - Critical for layout */
* {
  box-sizing: border-box;
  border-color: hsl(var(--border));
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-tap-highlight-color: transparent;
  line-height: 1.5;
}

@media (max-width: 640px) {
  html {
    font-size: 15px;
  }
}

body {
  margin: 0;
  padding: 0;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
}

/* Critical layout styles */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navigation - Critical for above-the-fold */
.navbar-container {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
}

/* Hero section - Critical for LCP */
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fdf8f6 0%, #f2e8e5 100%);
}

/* Critical typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
  color: hsl(var(--foreground));
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

@media (min-width: 640px) {
  h1 {
    font-size: 3rem;
  }
}

@media (min-width: 768px) {
  h1 {
    font-size: 3.75rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 4.5rem;
  }
}

p {
  margin: 0;
  line-height: 1.6;
}

/* Critical button styles */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background-color: var(--badhees-accent);
  color: white;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s ease;
  min-height: 44px;
}

.btn-primary:hover {
  background-color: #7a3d0f;
}

@media (min-width: 640px) {
  .btn-primary {
    padding: 0.875rem 1.75rem;
  }
}

/* Critical layout utilities */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

/* Critical flexbox utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* Critical spacing */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* Critical text utilities */
.text-center {
  text-align: center;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* Critical responsive utilities */
.hidden {
  display: none;
}

@media (min-width: 640px) {
  .sm\:block {
    display: block;
  }
  
  .sm\:hidden {
    display: none;
  }
  
  .sm\:flex {
    display: flex;
  }
}

@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  
  .md\:hidden {
    display: none;
  }
}

/* Critical loading state */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid hsl(var(--border));
  border-top: 2px solid var(--badhees-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Critical image styles */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Critical focus styles */
button:focus-visible,
a:focus-visible,
input:focus-visible {
  outline: 2px solid var(--badhees-accent);
  outline-offset: 2px;
}

/* Critical mobile optimizations */
@media (max-width: 640px) {
  button,
  .btn,
  a[role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  input,
  select,
  textarea {
    font-size: 16px; /* Prevents iOS zoom */
    min-height: 44px;
  }
}

/* Critical performance optimizations */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

button,
a,
[role="button"] {
  touch-action: manipulation;
}
