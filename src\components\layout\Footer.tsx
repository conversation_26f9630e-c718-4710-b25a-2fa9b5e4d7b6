
import React from "react";
import { Link } from "react-router-dom";
import {
  Mail,
  Phone,
  MapPin,
  Instagram,
  Youtube,
  ArrowRight,
  ChevronDown
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useIsMobile } from "@/hooks/use-mobile";

const Footer = () => {
  const isMobile = useIsMobile();

  const quickLinks = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products" },
    { name: "Custom Projects", path: "/custom-interiors" },
    { name: "About", path: "/about" },
  ];

  const customerServiceLinks = [
    { name: "FAQ", path: "/faq" },
    { name: "Shipping & Returns", path: "/shipping-returns" },
    { name: "Care Instructions", path: "/care-instructions" },
    { name: "Warranty", path: "/warranty" },
  ];

  const renderMobileFooter = () => (
    <div className="py-8">
      {/* Logo and Description */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-badhees-800 font-display mb-4">
          The Badhees
        </h2>
        <p className="text-sm text-badhees-600 mb-4">
          Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality.
        </p>
        <div className="flex space-x-4">
          <a
            href="https://www.instagram.com/thebadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="text-badhees-400 hover:text-badhees-600 transition-colors"
            aria-label="Instagram"
          >
            <Instagram className="h-5 w-5" />
          </a>
          <a
            href="https://www.youtube.com/@TheBadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="text-badhees-400 hover:text-badhees-600 transition-colors"
            aria-label="YouTube"
          >
            <Youtube className="h-5 w-5" />
          </a>
        </div>
      </div>

      {/* Accordion Links */}
      <Accordion type="single" collapsible className="w-full mb-6">
        <AccordionItem value="quick-links" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Quick Links
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-4 pt-2 pl-1 pb-2">
              {quickLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="text-base text-badhees-600 hover:text-badhees-accent flex items-center touch-target py-2"
                >
                  <ArrowRight className="h-4 w-4 mr-3" />
                  {link.name}
                </Link>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="customer-service" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Customer Service
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-4 pt-2 pl-1 pb-2">
              {customerServiceLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="text-base text-badhees-600 hover:text-badhees-accent flex items-center touch-target py-2"
                >
                  <ArrowRight className="h-4 w-4 mr-3" />
                  {link.name}
                </Link>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="contact" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Contact Us
          </AccordionTrigger>
          <AccordionContent>
            <ul className="space-y-4 pt-2 pb-2">
              <li className="flex items-start touch-target py-2">
                <MapPin className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-0.5" />
                <span className="text-base text-badhees-600">
                  The Badhees, Opposite Vasudha layout, Appajipura road, Koraluru, Bangalore 560067
                </span>
              </li>
              <li className="flex items-start touch-target py-2">
                <Phone className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-1" />
                <div className="flex flex-col">
                  <a href="tel:9108344363" className="text-base text-badhees-600">9108344363</a>
                  <a href="tel:8197705438" className="text-base text-badhees-600 mt-1">8197705438</a>
                </div>
              </li>
              <li className="flex items-center touch-target py-2">
                <Mail className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-base text-badhees-600"><EMAIL></a>
              </li>
            </ul>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );

  const renderDesktopFooter = () => (
    <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-4">
      {/* Column 1: Logo and Description */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-badhees-800 font-display">
          The Badhees
        </h2>
        <p className="text-sm text-badhees-600 max-w-xs">
          Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality.
        </p>
        <div className="flex space-x-4 pt-2">
          <a
            href="https://www.instagram.com/thebadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="text-badhees-400 hover:text-badhees-600 transition-colors"
            aria-label="Instagram"
          >
            <Instagram className="h-5 w-5" />
          </a>
          <a
            href="https://www.youtube.com/@TheBadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="text-badhees-400 hover:text-badhees-600 transition-colors"
            aria-label="YouTube"
          >
            <Youtube className="h-5 w-5" />
          </a>
        </div>
      </div>

      {/* Column 2: Quick Links */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Quick Links</h3>
        <ul className="space-y-2">
          {quickLinks.map((link) => (
            <li key={link.name}>
              <Link
                to={link.path}
                className="text-sm text-badhees-600 hover:text-badhees-accent flex items-center group"
              >
                <ArrowRight className="h-3 w-3 mr-2 opacity-0 -ml-5 group-hover:opacity-100 group-hover:ml-0 transition-all duration-300" />
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Column 3: Customer Service */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Customer Service</h3>
        <ul className="space-y-2">
          {customerServiceLinks.map((link) => (
            <li key={link.name}>
              <Link
                to={link.path}
                className="text-sm text-badhees-600 hover:text-badhees-accent flex items-center group"
              >
                <ArrowRight className="h-3 w-3 mr-2 opacity-0 -ml-5 group-hover:opacity-100 group-hover:ml-0 transition-all duration-300" />
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Column 4: Contact */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Contact Us</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <MapPin className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm text-badhees-600">
              The Badhees, Opposite Vasudha layout, Appajipura road, Koraluru, Bangalore 560067
            </span>
          </li>
          <li className="flex items-start">
            <Phone className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-1" />
            <div className="flex flex-col">
              <span className="text-sm text-badhees-600">9108344363</span>
              <span className="text-sm text-badhees-600 mt-1">8197705438</span>
            </div>
          </li>
          <li className="flex items-center">
            <Mail className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0" />
            <span className="text-sm text-badhees-600"><EMAIL></span>
          </li>
        </ul>
      </div>
    </div>
  );

  return (
    <footer className="bg-badhees-50 border-t border-badhees-100 safe-bottom">
      <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16">
        {isMobile ? renderMobileFooter() : renderDesktopFooter()}

        {/* Copyright - Same for both mobile and desktop */}
        <div className="mt-8 pt-6 border-t border-badhees-100">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-badhees-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} The Badhees. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link to="/privacy-policy" className="text-xs sm:text-sm text-badhees-400 hover:text-badhees-600 touch-target py-2">
                Privacy Policy
              </Link>
              <Link to="/terms-of-service" className="text-xs sm:text-sm text-badhees-400 hover:text-badhees-600 touch-target py-2">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
