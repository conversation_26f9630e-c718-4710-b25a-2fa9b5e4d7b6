import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { RefreshCw } from 'lucide-react';
import './pull-to-refresh.css';

interface PullToRefreshProps {
  children: ReactNode;
  onRefresh: () => Promise<void> | void;
  disabled?: boolean;
  threshold?: number;
  className?: string;
}

export function PullToRefresh({
  children,
  onRefresh,
  disabled = false,
  threshold = 100,
  className = '',
}: PullToRefreshProps) {
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [startY, setStartY] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = (e: TouchEvent) => {
    if (disabled || isRefreshing) return;

    const container = containerRef.current;
    if (!container) return;

    // Only trigger if we're exactly at the top of the page
    if (container.scrollTop > 0) return;

    // Also check if the document/window is at the top
    if (window.scrollY > 0 || document.documentElement.scrollTop > 0) return;

    setStartY(e.touches[0].clientY);
    // Don't set isPulling immediately, wait for actual downward movement
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (disabled || isRefreshing || startY === 0) return;

    const container = containerRef.current;
    if (!container) return;

    // Check if we're still at the top
    if (container.scrollTop > 0 || window.scrollY > 0 || document.documentElement.scrollTop > 0) {
      if (isPulling) {
        setIsPulling(false);
        setPullDistance(0);
      }
      return;
    }

    const currentY = e.touches[0].clientY;
    const distance = currentY - startY;

    // Only start pulling if there's significant downward movement (at least 15px)
    if (distance > 15) {
      if (!isPulling) {
        setIsPulling(true);
      }
      // Prevent default scrolling behavior only when actively pulling
      e.preventDefault();
      setPullDistance(Math.min(Math.max(0, distance - 15), threshold * 1.5));
    } else if (distance < -10) {
      // If user moves up significantly, reset everything
      setIsPulling(false);
      setPullDistance(0);
      setStartY(0);
    }
  };

  const handleTouchEnd = async () => {
    if (!isPulling || disabled || isRefreshing) return;

    setIsPulling(false);

    if (pullDistance >= threshold) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }

    setPullDistance(0);
    setStartY(0);
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isPulling, pullDistance, startY, threshold, disabled, isRefreshing]);

  const pullProgress = Math.min(pullDistance / threshold, 1);
  const shouldShowRefresh = pullDistance > 30;

  // Dynamic styles that must be inline due to real-time calculations based on touch events
  // These cannot be moved to external CSS as they depend on JavaScript state variables
  const containerTransform = isPulling ? `translateY(${Math.min(pullDistance * 0.5, 40)}px)` : 'none';
  const indicatorTransform = `translateY(${isPulling ? -40 + (pullDistance * 0.5) : isRefreshing ? 0 : -40}px)`;
  const iconRotation = isPulling && !isRefreshing ? `rotate(${pullProgress * 360}deg)` : 'none';
  const contentPadding = (shouldShowRefresh || isRefreshing) ? '60px' : '0px';

  return (
    <div
      ref={containerRef}
      className={`pull-to-refresh-container ${isPulling ? 'is-pulling' : ''} ${className}`}
      style={{ transform: containerTransform }}
    >
      {/* Pull to refresh indicator */}
      {(shouldShowRefresh || isRefreshing) && (
        <div
          className={`pull-to-refresh-indicator ${isPulling ? 'is-pulling' : ''}`}
          style={{ transform: indicatorTransform }}
        >
          <div className="flex items-center space-x-2 text-badhees-600">
            <RefreshCw
              className={`pull-to-refresh-icon ${isRefreshing ? 'is-refreshing' : ''}`}
              style={{ transform: iconRotation }}
            />
            <span className="text-sm font-medium">
              {isRefreshing
                ? 'Refreshing...'
                : pullDistance >= threshold
                ? 'Release to refresh'
                : 'Pull to refresh'}
            </span>
          </div>
        </div>
      )}

      {/* Content */}
      <div
        className="pull-to-refresh-content"
        style={{ paddingTop: contentPadding }}
      >
        {children}
      </div>
    </div>
  );
}

export default PullToRefresh;
