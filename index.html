<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <!-- Theme color is defined in the manifest file -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <title>The Badhees</title>
    <meta name="description" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <!-- Optimized font loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" /></noscript>
    <!-- Resource hints for performance -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="dns-prefetch" href="https://checkout.razorpay.com" />

    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.tsx" />
    <link rel="preload" href="/icons/favicon-32x32.svg" as="image" type="image/svg+xml" />

    <meta name="author" content="Lovable" />
    <meta property="og:image" content="/og-image.png" />
    <link rel="manifest" href="/manifest.webmanifest" />
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/icons/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/icons/favicon-16x16.svg" />
    <!-- Inline critical JavaScript -->
    <script>
      // Check if we have a redirect path from the 404 page
      (function() {
        const redirectPath = sessionStorage.getItem('redirectPath');
        if (redirectPath) {
          sessionStorage.removeItem('redirectPath');
          // We'll let the app handle the routing once it loads
          window.history.replaceState(null, null, redirectPath);
        }
      })();

      // Preload critical CSS after fonts load
      (function() {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(function() {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = '/src/index.css';
            document.head.appendChild(link);
          });
        }
      })();

      // Performance mark for measuring
      if ('performance' in window && 'mark' in performance) {
        performance.mark('html-parsed');
      }
    </script>
              <style id="critical-css">
:root{--background:0 0% 100%;--foreground:222.2 84% 4.9%;--card:0 0% 100%;--card-foreground:222.2 84% 4.9%;--primary:222.2 47.4% 11.2%;--primary-foreground:210 40% 98%;--secondary:210 40% 96.1%;--secondary-foreground:222.2 47.4% 11.2%;--muted:210 40% 96.1%;--muted-foreground:215.4 16.3% 46.9%;--accent:210 40% 96.1%;--accent-foreground:222.2 47.4% 11.2%;--destructive:0 84.2% 60.2%;--destructive-foreground:210 40% 98%;--border:214.3 31.8% 91.4%;--input:214.3 31.8% 91.4%;--ring:222.2 84% 4.9%;--radius:0.5rem;--badhees-50:#fdf8f6;--badhees-100:#f2e8e5;--badhees-200:#eaddd7;--badhees-300:#e0cec7;--badhees-400:#d2bab0;--badhees-500:#bfa094;--badhees-600:#a18072;--badhees-700:#8b6f5b;--badhees-800:#73624f;--badhees-900:#5d4f42;--badhees-accent:#8b4513} *{box-sizing:border-box;border-color:hsl(var(--border))} html{font-size:16px;scroll-behavior:smooth;-webkit-tap-highlight-color:transparent;line-height:1.5} @media (max-width:640px){html{font-size:15px} } body{margin:0;padding:0;background-color:hsl(var(--background));color:hsl(var(--foreground));font-family:Inter,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-feature-settings:"ss01","ss02","cv01","cv02","cv03"} #root{min-height:100vh;display:flex;flex-direction:column} .navbar-container{position:sticky;top:0;z-index:50;background-color:hsl(var(--background));border-bottom:1px solid hsl(var(--border))} .hero-section{min-height:60vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#fdf8f6 0%,#f2e8e5 100%)} h1,h2,h3,h4,h5,h6{margin:0;font-weight:600;line-height:1.2;color:hsl(var(--foreground))} h1{font-size:2.25rem;font-weight:700} @media (min-width:640px){h1{font-size:3rem} } @media (min-width:768px){h1{font-size:3.75rem} } @media (min-width:1024px){h1{font-size:4.5rem} } p{margin:0;line-height:1.6} .btn-primary{display:inline-flex;align-items:center;justify-content:center;padding:0.75rem 1.5rem;background-color:var(--badhees-accent);color:white;font-weight:500;border-radius:0.5rem;border:none;cursor:pointer;text-decoration:none;transition:background-color 0.2s ease;min-height:44px} .btn-primary:hover{background-color:#7a3d0f} @media (min-width:640px){.btn-primary{padding:0.875rem 1.75rem} } .container{width:100%;max-width:1400px;margin:0 auto;padding:0 1rem} @media (min-width:640px){.container{padding:0 1.5rem} } @media (min-width:768px){.container{padding:0 2rem} } .flex{display:flex} .flex-col{flex-direction:column} .items-center{align-items:center} .justify-center{justify-content:center} .justify-between{justify-content:space-between} .space-y-4 > * + *{margin-top:1rem} .space-y-6 > * + *{margin-top:1.5rem} .space-y-8 > * + *{margin-top:2rem} .text-center{text-align:center} .text-lg{font-size:1.125rem} .text-xl{font-size:1.25rem} .text-2xl{font-size:1.5rem} .text-3xl{font-size:1.875rem} .font-bold{font-weight:700} .font-semibold{font-weight:600} .font-medium{font-weight:500} .hidden{display:none} @media (min-width:640px){.sm\:block{display:block} .sm\:hidden{display:none} .sm\:flex{display:flex} } @media (min-width:768px){.md\:block{display:block} .md\:hidden{display:none} } .loading-spinner{width:2rem;height:2rem;border:2px solid hsl(var(--border));border-top:2px solid var(--badhees-accent);border-radius:50%;animation:spin 1s linear infinite} @keyframes spin{to{transform:rotate(360deg)} } img{max-width:100%;height:auto;display:block} button:focus-visible,a:focus-visible,input:focus-visible{outline:2px solid var(--badhees-accent);outline-offset:2px} @media (max-width:640px){button,.btn,a[role="button"]{min-height:44px;min-width:44px} input,select,textarea{font-size:16px;min-height:44px} } *{-webkit-tap-highlight-color:rgba(0,0,0,0.1)} button,a,[role="button"]{touch-action:manipulation}
    </style>
  </head>

  <body>
    <div id="root"></div>
    <!-- External script removed temporarily for debugging -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
