
import React, { useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import PageContainer from "@/components/layout/PageContainer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";
import {
  Clock,
  Home,
  Paintbrush,
  MessageSquare,
  Calendar,
  Check,
  Users
} from 'lucide-react';
import { Input } from "@/components/ui/input";
import AutoSlidingHeroBanner from '@/components/custom/AutoSlidingHeroBanner';
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { useQueryClient } from "@tanstack/react-query";
import { useTabVisibility } from "@/hooks/use-optimized-render";
import '@/styles/card.css';
import '@/styles/custom-projects-mobile.css';

interface ProjectCategory {
  id: string;
  title: string;
  description: string;
  image: string;
  features: string[];
}

const CustomInteriorProjects = () => {
  const queryClient = useQueryClient();

  // Handle tab visibility changes - refresh any cached project data
  const handleTabVisible = useCallback(() => {
    console.log('Tab became visible - refreshing custom projects data');
    queryClient.invalidateQueries({ queryKey: ['completedProjects'] });
    queryClient.invalidateQueries({ queryKey: ['project'] });
    queryClient.invalidateQueries({ queryKey: ['allPublishedProjects'] });
  }, [queryClient]);

  useTabVisibility(handleTabVisible);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    try {
      // Invalidate all project-related queries
      await queryClient.invalidateQueries({ queryKey: ['completedProjects'] });
      await queryClient.invalidateQueries({ queryKey: ['project'] });
      await queryClient.invalidateQueries({ queryKey: ['allPublishedProjects'] });
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [queryClient]);

  const projectCategories: ProjectCategory[] = [
    {
      id: "kitchen",
      title: "Kitchen Interiors",
      description: "Transform your kitchen into a functional and beautiful space with our custom-designed interiors.",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//projectcardkitchen1.jpeg",
      features: ["Custom cabinets", "Modern appliances", "Ergonomic layouts", "Premium countertops"]
    },
    {
      id: "bedroom",
      title: "Bedroom Designs",
      description: "Create a serene retreat with our custom bedroom designs that blend comfort and style.",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//projectcardbed.jpg",
      features: ["Space-efficient layouts", "Custom wardrobes", "Ambient lighting", "Luxury bedding solutions"]
    },
    {
      id: "living",
      title: "Living Room Interiors",
      description: "Design a welcoming living space that reflects your personality and meets your entertainment needs.",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//projectcardliving.jpeg",
      features: ["Entertainment setups", "Custom shelving", "Seating arrangements", "Accent lighting"]
    },
    {
      id: "office",
      title: "Office Furniture & Others",
      description: "Create a productive workspace with our ergonomic office designs and custom furniture for any space.",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//projectcardoffice.jpeg",
      features: ["Ergonomic workstations", "Storage solutions", "Custom furniture", "Specialty designs"]
    }
  ];

  const faqs = [
    {
      question: "How long does a typical interior project take?",
      answer: "Project timelines vary based on scope and complexity. A single room redesign may take 4-6 weeks, while full home interiors can take 3-6 months. We provide detailed timelines during consultation."
    },
    {
      question: "What materials do you use in your projects?",
      answer: "We work with a wide range of premium materials, from sustainable woods and eco-friendly fabrics to luxury marbles and metals. All materials are selected for quality, durability, and aesthetic appeal, tailored to your preferences and budget."
    },
    {
      question: "Can I customize every aspect of my interior project?",
      answer: "Absolutely! Our service is fully bespoke. From layouts and color schemes to materials and furnishings, everything can be customized to your exact specifications, style preferences, and functional requirements."
    },
    {
      question: "How do you handle budgeting for projects?",
      answer: "We work with you to establish a clear budget at the beginning of the process. Our team provides transparent pricing breakdowns and helps you prioritize elements to ensure your project stays within budget while achieving your design goals."
    },
    {
      question: "Do you handle the entire project from design to installation?",
      answer: "Yes, we offer end-to-end service. Our team handles everything from initial concept designs and material selection to coordination with contractors, furniture procurement, and final installation and styling."
    }
  ];


  useEffect(() => {
    // Check if there's a hash in the URL
    if (window.location.hash) {
      // Get the element with the ID from the hash
      const element = document.getElementById(window.location.hash.substring(1));
      if (element) {
        // Wait a bit for the page to fully render before scrolling
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } else {
        // If no matching element, scroll to top
        window.scrollTo(0, 0);
      }
    } else {
      // No hash, scroll to top
      window.scrollTo(0, 0);
    }
  }, []);

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-20 relative">
          <AutoSlidingHeroBanner
            categories={projectCategories}
            autoSlideInterval={3000}
          />
        </div>

      <section className="py-12 md:py-16 bg-badhees-50 custom-projects-section">
        <PageContainer>
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-4xl font-bold text-badhees-800 mb-4 md:mb-6 custom-projects-title">
              Custom Interior Solutions
            </h2>
            <p className="text-lg md:text-xl text-badhees-600 mb-8 custom-projects-description">
              Get a fully customized interior solution tailored to your home's style and needs. Our expert designers work with you every step of the way to create spaces that are both beautiful and functional.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8 mt-8 md:mt-12">
              <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center">
                <div className="bg-badhees-100 p-3 rounded-full mb-4">
                  <Paintbrush className="h-6 w-6 md:h-8 md:w-8 text-badhees-accent" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-badhees-800 mb-2">Personalized Design</h3>
                <p className="text-badhees-600 text-center">Tailored solutions that reflect your unique style and functional needs</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center">
                <div className="bg-badhees-100 p-3 rounded-full mb-4">
                  <Clock className="h-6 w-6 md:h-8 md:w-8 text-badhees-accent" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-badhees-800 mb-2">Efficient Process</h3>
                <p className="text-badhees-600 text-center">Streamlined approach from concept to completion, saving you time and effort</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center sm:col-span-2 md:col-span-1">
                <div className="bg-badhees-100 p-3 rounded-full mb-4">
                  <Home className="h-6 w-6 md:h-8 md:w-8 text-badhees-accent" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-badhees-800 mb-2">Quality Craftmanship</h3>
                <p className="text-badhees-600 text-center">Premium materials and expert execution for interiors that last</p>
              </div>
            </div>
          </div>
        </PageContainer>
      </section>

      <section className="py-12 md:py-16 custom-projects-section">
        <PageContainer>
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-4xl font-bold text-badhees-800 mb-4 custom-projects-title">
              Project Categories
            </h2>
            <p className="text-lg md:text-xl text-badhees-600 max-w-3xl mx-auto custom-projects-description">
              Explore our range of specialized interior design services for every room in your home
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8">
            {projectCategories.map((category, index) => (
              <div
                key={category.id}
                className={`project-category-card animate-fade-in animation-delay-${Math.min(index + 1, 5) * 100}`}
              >
                {/* Image container with fixed aspect ratio */}
                <div className="project-category-image">
                  <img
                    src={category.image}
                    alt={category.title}
                    loading="lazy"
                  />
                </div>
                <div className="project-category-content">
                  <h3 className="project-category-title">{category.title}</h3>
                  <p className="project-category-description">{category.description}</p>
                  <div className="project-category-features">
                    <h4 className="project-category-features-title">Features:</h4>
                    <ul className="project-category-features-list">
                      {category.features.map((feature, idx) => (
                        <li key={idx} className="project-category-feature-item">
                          <Check className="project-category-feature-icon" />
                          <span className="line-clamp-1">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="project-category-button">
                    <Button
                      asChild
                      variant="outline"
                      className="w-full border-badhees-800 text-badhees-800 hover:bg-badhees-800 hover:text-white min-h-[44px] text-sm sm:text-base"
                    >
                      <Link to={`/completed-projects/${category.id}`}>View Completed Projects</Link>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </PageContainer>
      </section>

      <section id="contact-section" className="py-12 md:py-16 bg-badhees-800 text-white custom-projects-section">
        <PageContainer>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 items-center">
            <div>
              <h2 className="text-2xl md:text-4xl font-bold mb-4 custom-projects-title">
                Ready to Transform Your Space?
              </h2>
              <p className="text-lg md:text-xl text-white/80 mb-6 custom-projects-description">
                Book a free consultation with our interior design experts and take the first step toward your dream home.
              </p>
              <div className="flex flex-wrap gap-3">
                <Button
                  size="lg"
                  className="bg-badhees-accent hover:bg-badhees-accent/90 flex items-center"
                >
                  <Calendar className="h-5 w-5 mr-2" />
                  Book a Consultation
                </Button>
                <Button
                  size="lg"
                  className="bg-white text-badhees-800 hover:bg-badhees-accent hover:text-white flex items-center"
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Contact Our Team
                </Button>
              </div>
              <div className="mt-8 flex items-center">
                <Users className="h-5 w-5 mr-2 text-badhees-accent" />
                <span className="text-white/80">Join over 500+ satisfied clients</span>
              </div>
            </div>

            <div className="bg-white/10 p-4 md:p-6 rounded-lg backdrop-blur-sm border-2 border-badhees-accent/50 shadow-lg">
              <h3 className="text-xl font-semibold mb-6">Request Information</h3>
              <form className="space-y-4 custom-projects-form">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-white/80 mb-1">
                      Name
                    </label>
                    <Input id="name" placeholder="Your name" className="bg-white/20 border-white/20 text-white" />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-white/80 mb-1">
                      Email
                    </label>
                    <Input id="email" type="email" placeholder="Your email" className="bg-white/20 border-white/20 text-white" />
                  </div>
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-white/80 mb-1">
                    Phone
                  </label>
                  <Input id="phone" placeholder="Your phone number" className="bg-white/20 border-white/20 text-white" />
                </div>
                <div>
                  <label htmlFor="project-type" className="block text-sm font-medium text-white/80 mb-1">
                    Project Type
                  </label>
                  <select
                    id="project-type"
                    className="w-full rounded-md bg-white/20 border-white/20 text-white py-2 px-3"
                  >
                    <option value="">Select project type</option>
                    <option value="kitchen">Kitchen Interior</option>
                    <option value="bedroom">Bedroom Design</option>
                    <option value="living">Living Room Interior</option>
                    <option value="office">Office Design</option>
                    <option value="full">Full Home Interior</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-white/80 mb-1">
                    Message
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    placeholder="Briefly describe your project"
                    className="w-full rounded-md bg-white/20 border-white/20 text-white py-2 px-3"
                  ></textarea>
                </div>
                <div>
                  <Button type="submit" className="w-full bg-badhees-accent hover:bg-badhees-accent/90">
                    Submit Request
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </PageContainer>
      </section>

      <section className="py-12 md:py-16 custom-projects-section">
        <PageContainer>
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-4xl font-bold text-badhees-800 mb-4 custom-projects-faq-title">
              Frequently Asked Questions
            </h2>
            <p className="text-lg md:text-xl text-badhees-600 max-w-2xl mx-auto custom-projects-faq-description">
              Find answers to common questions about our custom interior design services
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq, index) => (
                <AccordionItem key={index} value={`faq-${index}`} className="custom-projects-accordion-item">
                  <AccordionTrigger className="text-left text-base md:text-lg font-medium text-badhees-800 custom-projects-accordion-trigger">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-badhees-600 custom-projects-accordion-content">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <div className="mt-8 md:mt-12 text-center">
            <p className="text-badhees-600 mb-4">
              Have more questions? We're here to help!
            </p>
            <Button
              variant="outline"
              className="border-badhees-800 text-badhees-800 hover:bg-badhees-800 hover:text-white"
              onClick={() => document.getElementById('contact-section')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Contact Our Support Team
            </Button>
          </div>
        </PageContainer>
        </section>

        <Footer />
      </PullToRefresh>
    </div>
  );
};

export default CustomInteriorProjects;
