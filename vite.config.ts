import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { cssOptimizationPlugin, resourceHintsPlugin } from "./src/plugins/css-optimization";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Add history API fallback for SPA routing
    historyApiFallback: true,
    proxy: {
      '/api/razorpay': {
        // Use Vercel dev server when available, fallback to Node.js server
        target: process.env.VERCEL_URL ? `http://localhost:3000` : `http://localhost:${process.env.VITE_SERVER_PORT || 3001}`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
    cssOptimizationPlugin(),
    resourceHintsPlugin(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core React libraries
          if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
            return 'react-vendor';
          }

          // Radix UI components - split into smaller chunks
          if (id.includes('@radix-ui')) {
            if (id.includes('dialog') || id.includes('alert-dialog') || id.includes('popover')) {
              return 'radix-dialogs';
            }
            if (id.includes('dropdown') || id.includes('select') || id.includes('menubar')) {
              return 'radix-menus';
            }
            if (id.includes('form') || id.includes('input') || id.includes('checkbox')) {
              return 'radix-forms';
            }
            return 'radix-ui';
          }

          // React Query
          if (id.includes('@tanstack/react-query')) {
            return 'query-vendor';
          }

          // Form libraries
          if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('zod')) {
            return 'form-vendor';
          }

          // Charts - only load when needed
          if (id.includes('recharts') || id.includes('d3-')) {
            return 'chart-vendor';
          }

          // Date utilities
          if (id.includes('date-fns')) {
            return 'date-vendor';
          }

          // Supabase
          if (id.includes('@supabase')) {
            return 'supabase-vendor';
          }

          // Lucide icons
          if (id.includes('lucide-react')) {
            return 'icons-vendor';
          }

          // Admin pages - lazy load completely
          if (id.includes('/pages/Admin')) {
            return 'admin-pages';
          }

          // Other vendor libraries
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      }
    },
    chunkSizeWarningLimit: 500, // Reduce to 500KB for better optimization
    target: 'es2020', // Better browser support while maintaining performance
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // Remove specific console methods
        passes: 2 // Multiple passes for better compression
      },
      mangle: {
        safari10: true // Better Safari support
      }
    },
    // Enable CSS code splitting
    cssCodeSplit: true,
    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom'
      ],
      exclude: [
        '@tanstack/react-query-devtools' // Exclude dev tools from optimization
      ]
    }
  },

}));
