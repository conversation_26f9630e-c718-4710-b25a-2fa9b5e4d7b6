import { Plugin } from 'vite';

/**
 * CSS Optimization Plugin for Vite
 * 
 * This plugin optimizes CSS loading by:
 * 1. Deferring non-critical CSS
 * 2. Adding preload hints for CSS files
 * 3. Optimizing CSS delivery
 */
export function cssOptimizationPlugin(): Plugin {
  return {
    name: 'css-optimization',
    apply: 'build',
    generateBundle(options, bundle) {
      // Find CSS files in the bundle
      const cssFiles = Object.keys(bundle).filter(fileName => 
        fileName.endsWith('.css') && !fileName.includes('critical')
      );

      // Find the main HTML file
      const htmlFiles = Object.keys(bundle).filter(fileName => 
        fileName.endsWith('.html')
      );

      htmlFiles.forEach(htmlFileName => {
        const htmlFile = bundle[htmlFileName];
        if (htmlFile.type === 'asset' && typeof htmlFile.source === 'string') {
          let htmlContent = htmlFile.source;

          // Add preload hints for CSS files
          cssFiles.forEach(cssFile => {
            const preloadHint = `<link rel="preload" href="/${cssFile}" as="style" onload="this.onload=null;this.rel='stylesheet'">`;
            const noscriptFallback = `<noscript><link rel="stylesheet" href="/${cssFile}"></noscript>`;
            
            // Insert preload hints before closing head tag
            htmlContent = htmlContent.replace(
              '</head>',
              `    ${preloadHint}\n    ${noscriptFallback}\n  </head>`
            );
          });

          // Add CSS loading optimization script
          const cssLoadingScript = `
    <script>
      // Optimize CSS loading
      (function() {
        // Load CSS asynchronously
        function loadCSS(href) {
          var link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.media = 'print';
          link.onload = function() {
            this.media = 'all';
          };
          document.head.appendChild(link);
        }
        
        // Load non-critical CSS after page load
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            requestAnimationFrame(function() {
              ${cssFiles.map(cssFile => `loadCSS('/${cssFile}');`).join('\n              ')}
            });
          });
        } else {
          requestAnimationFrame(function() {
            ${cssFiles.map(cssFile => `loadCSS('/${cssFile}');`).join('\n            ')}
          });
        }
      })();
    </script>`;

          // Insert the script before closing head tag
          htmlContent = htmlContent.replace(
            '</head>',
            `${cssLoadingScript}\n  </head>`
          );

          // Update the bundle
          htmlFile.source = htmlContent;
        }
      });
    }
  };
}

/**
 * Resource Hints Plugin
 * 
 * Adds resource hints for better performance
 */
export function resourceHintsPlugin(): Plugin {
  return {
    name: 'resource-hints',
    apply: 'build',
    generateBundle(options, bundle) {
      const htmlFiles = Object.keys(bundle).filter(fileName => 
        fileName.endsWith('.html')
      );

      htmlFiles.forEach(htmlFileName => {
        const htmlFile = bundle[htmlFileName];
        if (htmlFile.type === 'asset' && typeof htmlFile.source === 'string') {
          let htmlContent = htmlFile.source;

          // Add resource hints for common external domains
          const resourceHints = `
    <!-- Additional resource hints -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="preconnect" href="https://api.supabase.co" crossorigin>`;

          // Insert resource hints after existing preconnect tags
          htmlContent = htmlContent.replace(
            '<link rel="dns-prefetch" href="https://checkout.razorpay.com" />',
            `<link rel="dns-prefetch" href="https://checkout.razorpay.com" />${resourceHints}`
          );

          // Update the bundle
          htmlFile.source = htmlContent;
        }
      });
    }
  };
}
