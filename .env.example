# ===========================================
# THE BADHEES - ENVIRONMENT CONFIGURATION
# ===========================================
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local to version control

# ===========================================
# SUPABASE CONFIGURATION
# ===========================================
# Get these from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ===========================================
# RAZORPAY CONFIGURATION
# ===========================================
# Frontend key (public - safe to expose in browser)
VITE_RAZORPAY_KEY_ID=rzp_test_your_key_id_here

# Backend keys (private - for API routes only)
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_SECRET=your_razorpay_secret_key_here
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_here

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Server port for local development
VITE_SERVER_PORT=3001

# Enable debug mode (optional)
VITE_DEBUG_MODE=true

# ===========================================
# PRODUCTION DEPLOYMENT NOTES
# ===========================================
# For production deployment:
# 1. Use rzp_live_ keys instead of rzp_test_
# 2. Set VITE_DEBUG_MODE=false
# 3. Set these environment variables in Vercel dashboard
# 4. NEVER put production keys in this file

# Email Configuration
# Add your preferred email service configuration here
# Examples:
# EMAIL_SERVICE=sendgrid
# EMAIL_API_KEY=your_api_key
# EMAIL_FROM=<EMAIL>
