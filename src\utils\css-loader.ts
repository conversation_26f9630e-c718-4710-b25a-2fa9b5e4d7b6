/**
 * Advanced CSS Loading Utilities
 * 
 * This module provides utilities for optimized CSS loading
 * to eliminate render-blocking resources
 */

/**
 * Load CSS asynchronously without blocking render
 */
export function loadCSSAsync(href: string, media: string = 'all'): Promise<void> {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print'; // Load as print to avoid blocking
    
    link.onload = () => {
      link.media = media; // Switch to target media
      resolve();
    };
    
    link.onerror = () => {
      reject(new Error(`Failed to load CSS: ${href}`));
    };
    
    document.head.appendChild(link);
  });
}

/**
 * Preload CSS with fallback
 */
export function preloadCSS(href: string): void {
  // Check if already loaded
  const existing = document.querySelector(`link[href="${href}"]`);
  if (existing) return;
  
  // Create preload link
  const preloadLink = document.createElement('link');
  preloadLink.rel = 'preload';
  preloadLink.as = 'style';
  preloadLink.href = href;
  
  // Create actual stylesheet link
  const styleLink = document.createElement('link');
  styleLink.rel = 'stylesheet';
  styleLink.href = href;
  styleLink.media = 'print';
  
  preloadLink.onload = () => {
    styleLink.media = 'all';
  };
  
  document.head.appendChild(preloadLink);
  document.head.appendChild(styleLink);
}

/**
 * Load critical CSS for specific components
 */
export function loadCriticalCSS(componentName: string): void {
  const criticalCSS = getCriticalCSSForComponent(componentName);
  if (criticalCSS) {
    injectCSS(criticalCSS, `critical-${componentName}`);
  }
}

/**
 * Inject CSS directly into the document
 */
export function injectCSS(css: string, id?: string): void {
  const style = document.createElement('style');
  if (id) style.id = id;
  style.textContent = css;
  document.head.appendChild(style);
}

/**
 * Get critical CSS for specific components
 */
function getCriticalCSSForComponent(componentName: string): string | null {
  const criticalCSS: Record<string, string> = {
    navbar: `
      .navbar-container {
        position: sticky;
        top: 0;
        z-index: 50;
        background-color: hsl(var(--background));
        border-bottom: 1px solid hsl(var(--border));
      }
    `,
    hero: `
      .hero-section {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #fdf8f6 0%, #f2e8e5 100%);
      }
    `,
    loading: `
      .loading-spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid hsl(var(--border));
        border-top: 2px solid var(--badhees-accent);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    `
  };
  
  return criticalCSS[componentName] || null;
}

/**
 * Load CSS based on viewport and connection
 */
export function loadAdaptiveCSS(): void {
  // Check connection quality
  const connection = (navigator as any).connection;
  const isSlowConnection = connection && (
    connection.effectiveType === 'slow-2g' || 
    connection.effectiveType === '2g' ||
    connection.saveData
  );
  
  // Check viewport size
  const isSmallViewport = window.innerWidth < 768;
  
  if (isSlowConnection) {
    // Load minimal CSS for slow connections
    loadCSSAsync('/assets/minimal.css');
  } else if (isSmallViewport) {
    // Load mobile-optimized CSS first
    loadCSSAsync('/assets/mobile.css').then(() => {
      // Load desktop CSS later if needed
      if (window.innerWidth >= 768) {
        loadCSSAsync('/assets/desktop.css');
      }
    });
  } else {
    // Load full CSS for fast connections and large viewports
    loadCSSAsync('/assets/index.css');
  }
}

/**
 * Remove unused CSS based on current page
 */
export function removeUnusedCSS(currentPage: string): void {
  const unusedSelectors = getUnusedSelectorsForPage(currentPage);
  
  unusedSelectors.forEach(selector => {
    try {
      const rules = Array.from(document.styleSheets).flatMap(sheet => {
        try {
          return Array.from(sheet.cssRules || []);
        } catch {
          return [];
        }
      });
      
      rules.forEach(rule => {
        if (rule instanceof CSSStyleRule && rule.selectorText === selector) {
          rule.style.display = 'none';
        }
      });
    } catch (error) {
      console.warn('Could not remove unused CSS:', error);
    }
  });
}

/**
 * Get unused selectors for specific pages
 */
function getUnusedSelectorsForPage(page: string): string[] {
  const unusedSelectors: Record<string, string[]> = {
    home: ['.admin-only', '.checkout-only'],
    admin: ['.public-only', '.checkout-only'],
    checkout: ['.admin-only', '.home-hero'],
    products: ['.admin-only', '.home-hero', '.checkout-only']
  };
  
  return unusedSelectors[page] || [];
}

/**
 * Initialize optimized CSS loading
 */
export function initOptimizedCSSLoading(): void {
  // Load critical CSS immediately
  loadCriticalCSS('navbar');
  loadCriticalCSS('loading');
  
  // Load adaptive CSS based on conditions
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadAdaptiveCSS);
  } else {
    loadAdaptiveCSS();
  }
  
  // Remove unused CSS after page load
  window.addEventListener('load', () => {
    const currentPage = window.location.pathname.split('/')[1] || 'home';
    removeUnusedCSS(currentPage);
  });
}
