/**
 * Service Worker Registration
 * 
 * This module handles service worker registration for performance optimization
 */

export async function registerServiceWorker(): Promise<void> {
  if (!('serviceWorker' in navigator)) {
    console.log('Service Worker not supported');
    return;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    console.log('Service Worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New service worker is available
            showUpdateNotification();
          }
        });
      }
    });

    // Handle controller change
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      // Reload page when new service worker takes control
      window.location.reload();
    });

  } catch (error) {
    console.error('Service Worker registration failed:', error);
  }
}

function showUpdateNotification(): void {
  // Show a notification to the user that an update is available
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('App Update Available', {
      body: 'A new version of the app is available. Refresh to update.',
      icon: '/icons/favicon-32x32.svg'
    });
  } else {
    // Fallback to console log or custom UI notification
    console.log('App update available. Refresh to update.');
  }
}

export async function unregisterServiceWorker(): Promise<void> {
  if (!('serviceWorker' in navigator)) {
    return;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      await registration.unregister();
      console.log('Service Worker unregistered');
    }
  } catch (error) {
    console.error('Service Worker unregistration failed:', error);
  }
}

// Check if service worker is supported and register it
export function initServiceWorker(): void {
  if (import.meta.env.PROD) {
    // Only register in production
    window.addEventListener('load', () => {
      registerServiceWorker();
    });
  }
}
