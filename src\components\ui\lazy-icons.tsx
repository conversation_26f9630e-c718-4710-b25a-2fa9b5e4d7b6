import { lazy, Suspense } from 'react';
import { LucideProps } from 'lucide-react';

// Create a generic icon loader
const createLazyIcon = (iconName: string) => 
  lazy(() => 
    import('lucide-react').then(module => ({ 
      default: (module as any)[iconName] 
    }))
  );

// Commonly used icons - load immediately
export { 
  Home, 
  ShoppingCart, 
  User, 
  Search, 
  Menu, 
  X,
  ChevronDown,
  ChevronRight,
  Star,
  Heart,
  Eye,
  Plus,
  Minus
} from 'lucide-react';

// Less common icons - lazy load
const LazyShoppingBag = createLazyIcon('ShoppingBag');
const LazyPackage = createLazyIcon('Package');
const LazyTruck = createLazyIcon('Truck');
const LazyRefreshCw = createLazyIcon('RefreshCw');
const LazyShield = createLazyIcon('Shield');
const LazyZoomIn = createLazyIcon('ZoomIn');
const LazyZoomOut = createLazyIcon('ZoomOut');
const LazyLoader2 = createLazyIcon('Loader2');
const LazyScissors = createLazyIcon('Scissors');
const LazyCheck = createLazyIcon('Check');
const LazyAlertCircle = createLazyIcon('AlertCircle');
const LazyChevronLeft = createLazyIcon('ChevronLeft');
const LazyFileText = createLazyIcon('FileText');
const LazyShare2 = createLazyIcon('Share2');
const LazyArrowRight = createLazyIcon('ArrowRight');
const LazyMail = createLazyIcon('Mail');
const LazyPhone = createLazyIcon('Phone');
const LazyMapPin = createLazyIcon('MapPin');
const LazyInstagram = createLazyIcon('Instagram');
const LazyYoutube = createLazyIcon('Youtube');
const LazyLayoutDashboard = createLazyIcon('LayoutDashboard');
const LazySettings = createLazyIcon('Settings');
const LazyGrid = createLazyIcon('Grid');
const LazyBookOpen = createLazyIcon('BookOpen');
const LazyCreditCard = createLazyIcon('CreditCard');
const LazyPackageOpen = createLazyIcon('PackageOpen');
const LazyMessageSquare = createLazyIcon('MessageSquare');
const LazyCalendar = createLazyIcon('Calendar');
const LazyArrowUpDown = createLazyIcon('ArrowUpDown');
const LazyFilter = createLazyIcon('Filter');
const LazyDownload = createLazyIcon('Download');
const LazyUpload = createLazyIcon('Upload');
const LazyEdit = createLazyIcon('Edit');
const LazyTrash = createLazyIcon('Trash');
const LazyMoreHorizontal = createLazyIcon('MoreHorizontal');

// Icon wrapper with suspense
const IconWrapper = ({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}) => (
  <Suspense fallback={fallback}>
    {children}
  </Suspense>
);

// Lazy icon components
export const ShoppingBag = (props: LucideProps) => (
  <IconWrapper>
    <LazyShoppingBag {...props} />
  </IconWrapper>
);

export const Package = (props: LucideProps) => (
  <IconWrapper>
    <LazyPackage {...props} />
  </IconWrapper>
);

export const Truck = (props: LucideProps) => (
  <IconWrapper>
    <LazyTruck {...props} />
  </IconWrapper>
);

export const RefreshCw = (props: LucideProps) => (
  <IconWrapper>
    <LazyRefreshCw {...props} />
  </IconWrapper>
);

export const Shield = (props: LucideProps) => (
  <IconWrapper>
    <LazyShield {...props} />
  </IconWrapper>
);

export const ZoomIn = (props: LucideProps) => (
  <IconWrapper>
    <LazyZoomIn {...props} />
  </IconWrapper>
);

export const ZoomOut = (props: LucideProps) => (
  <IconWrapper>
    <LazyZoomOut {...props} />
  </IconWrapper>
);

export const Loader2 = (props: LucideProps) => (
  <IconWrapper>
    <LazyLoader2 {...props} />
  </IconWrapper>
);

export const Scissors = (props: LucideProps) => (
  <IconWrapper>
    <LazyScissors {...props} />
  </IconWrapper>
);

export const Check = (props: LucideProps) => (
  <IconWrapper>
    <LazyCheck {...props} />
  </IconWrapper>
);

export const AlertCircle = (props: LucideProps) => (
  <IconWrapper>
    <LazyAlertCircle {...props} />
  </IconWrapper>
);

export const ChevronLeft = (props: LucideProps) => (
  <IconWrapper>
    <LazyChevronLeft {...props} />
  </IconWrapper>
);

export const FileText = (props: LucideProps) => (
  <IconWrapper>
    <LazyFileText {...props} />
  </IconWrapper>
);

export const Share2 = (props: LucideProps) => (
  <IconWrapper>
    <LazyShare2 {...props} />
  </IconWrapper>
);

export const ArrowRight = (props: LucideProps) => (
  <IconWrapper>
    <LazyArrowRight {...props} />
  </IconWrapper>
);

export const Mail = (props: LucideProps) => (
  <IconWrapper>
    <LazyMail {...props} />
  </IconWrapper>
);

export const Phone = (props: LucideProps) => (
  <IconWrapper>
    <LazyPhone {...props} />
  </IconWrapper>
);

export const MapPin = (props: LucideProps) => (
  <IconWrapper>
    <LazyMapPin {...props} />
  </IconWrapper>
);

export const Instagram = (props: LucideProps) => (
  <IconWrapper>
    <LazyInstagram {...props} />
  </IconWrapper>
);

export const Youtube = (props: LucideProps) => (
  <IconWrapper>
    <LazyYoutube {...props} />
  </IconWrapper>
);

export const LayoutDashboard = (props: LucideProps) => (
  <IconWrapper>
    <LazyLayoutDashboard {...props} />
  </IconWrapper>
);

export const Settings = (props: LucideProps) => (
  <IconWrapper>
    <LazySettings {...props} />
  </IconWrapper>
);

export const Grid = (props: LucideProps) => (
  <IconWrapper>
    <LazyGrid {...props} />
  </IconWrapper>
);

export const BookOpen = (props: LucideProps) => (
  <IconWrapper>
    <LazyBookOpen {...props} />
  </IconWrapper>
);

export const CreditCard = (props: LucideProps) => (
  <IconWrapper>
    <LazyCreditCard {...props} />
  </IconWrapper>
);

export const PackageOpen = (props: LucideProps) => (
  <IconWrapper>
    <LazyPackageOpen {...props} />
  </IconWrapper>
);

export const MessageSquare = (props: LucideProps) => (
  <IconWrapper>
    <LazyMessageSquare {...props} />
  </IconWrapper>
);

export const Calendar = (props: LucideProps) => (
  <IconWrapper>
    <LazyCalendar {...props} />
  </IconWrapper>
);

export const ArrowUpDown = (props: LucideProps) => (
  <IconWrapper>
    <LazyArrowUpDown {...props} />
  </IconWrapper>
);

export const Filter = (props: LucideProps) => (
  <IconWrapper>
    <LazyFilter {...props} />
  </IconWrapper>
);

export const Download = (props: LucideProps) => (
  <IconWrapper>
    <LazyDownload {...props} />
  </IconWrapper>
);

export const Upload = (props: LucideProps) => (
  <IconWrapper>
    <LazyUpload {...props} />
  </IconWrapper>
);

export const Edit = (props: LucideProps) => (
  <IconWrapper>
    <LazyEdit {...props} />
  </IconWrapper>
);

export const Trash = (props: LucideProps) => (
  <IconWrapper>
    <LazyTrash {...props} />
  </IconWrapper>
);

export const MoreHorizontal = (props: LucideProps) => (
  <IconWrapper>
    <LazyMoreHorizontal {...props} />
  </IconWrapper>
);
