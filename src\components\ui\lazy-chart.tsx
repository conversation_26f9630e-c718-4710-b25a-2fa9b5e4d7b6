import { lazy, Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load chart components to reduce initial bundle size
const LazyBarChart = lazy(() => 
  import('recharts').then(module => ({ default: module.BarChart }))
);

const Lazy<PERSON>ine<PERSON>hart = lazy(() => 
  import('recharts').then(module => ({ default: module.LineChart }))
);

const <PERSON>zy<PERSON>ie<PERSON>hart = lazy(() => 
  import('recharts').then(module => ({ default: module.PieChart }))
);

const Lazy<PERSON>reaChart = lazy(() => 
  import('recharts').then(module => ({ default: module.AreaChart }))
);

// Lazy load chart components
const LazyBar = lazy(() => 
  import('recharts').then(module => ({ default: module.Bar }))
);

const LazyLine = lazy(() => 
  import('recharts').then(module => ({ default: module.Line }))
);

const Lazy<PERSON>rea = lazy(() => 
  import('recharts').then(module => ({ default: module.Area }))
);

const <PERSON>zy<PERSON>ie = lazy(() => 
  import('recharts').then(module => ({ default: module.Pie }))
);

const LazyCell = lazy(() => 
  import('recharts').then(module => ({ default: module.Cell }))
);

const LazyXAxis = lazy(() => 
  import('recharts').then(module => ({ default: module.XAxis }))
);

const LazyYAxis = lazy(() => 
  import('recharts').then(module => ({ default: module.YAxis }))
);

const LazyCartesianGrid = lazy(() => 
  import('recharts').then(module => ({ default: module.CartesianGrid }))
);

const LazyTooltip = lazy(() => 
  import('recharts').then(module => ({ default: module.Tooltip }))
);

const LazyLegend = lazy(() => 
  import('recharts').then(module => ({ default: module.Legend }))
);

const LazyResponsiveContainer = lazy(() => 
  import('recharts').then(module => ({ default: module.ResponsiveContainer }))
);

// Chart loading skeleton
const ChartSkeleton = ({ height = 300 }: { height?: number }) => (
  <div className="w-full" style={{ height }}>
    <Skeleton className="w-full h-full rounded-lg" />
  </div>
);

// Wrapper components with lazy loading
export const BarChart = (props: any) => (
  <Suspense fallback={<ChartSkeleton height={props.height} />}>
    <LazyBarChart {...props} />
  </Suspense>
);

export const LineChart = (props: any) => (
  <Suspense fallback={<ChartSkeleton height={props.height} />}>
    <LazyLineChart {...props} />
  </Suspense>
);

export const PieChart = (props: any) => (
  <Suspense fallback={<ChartSkeleton height={props.height} />}>
    <LazyPieChart {...props} />
  </Suspense>
);

export const AreaChart = (props: any) => (
  <Suspense fallback={<ChartSkeleton height={props.height} />}>
    <LazyAreaChart {...props} />
  </Suspense>
);

export const Bar = (props: any) => (
  <Suspense fallback={null}>
    <LazyBar {...props} />
  </Suspense>
);

export const Line = (props: any) => (
  <Suspense fallback={null}>
    <LazyLine {...props} />
  </Suspense>
);

export const Area = (props: any) => (
  <Suspense fallback={null}>
    <LazyArea {...props} />
  </Suspense>
);

export const Pie = (props: any) => (
  <Suspense fallback={null}>
    <LazyPie {...props} />
  </Suspense>
);

export const Cell = (props: any) => (
  <Suspense fallback={null}>
    <LazyCell {...props} />
  </Suspense>
);

export const XAxis = (props: any) => (
  <Suspense fallback={null}>
    <LazyXAxis {...props} />
  </Suspense>
);

export const YAxis = (props: any) => (
  <Suspense fallback={null}>
    <LazyYAxis {...props} />
  </Suspense>
);

export const CartesianGrid = (props: any) => (
  <Suspense fallback={null}>
    <LazyCartesianGrid {...props} />
  </Suspense>
);

export const Tooltip = (props: any) => (
  <Suspense fallback={null}>
    <LazyTooltip {...props} />
  </Suspense>
);

export const Legend = (props: any) => (
  <Suspense fallback={null}>
    <LazyLegend {...props} />
  </Suspense>
);

export const ResponsiveContainer = (props: any) => (
  <Suspense fallback={<ChartSkeleton height={props.height} />}>
    <LazyResponsiveContainer {...props} />
  </Suspense>
);
