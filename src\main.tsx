import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import App from './App.tsx'
import './index.css'
import { performanceMonitor } from './utils/performance-monitor'
import { initOptimizedCSSLoading } from './utils/css-loader'
import { initServiceWorker } from './utils/sw-register'

// Initialize performance monitoring and optimizations
performanceMonitor.mark('app-start');
initOptimizedCSSLoading();
initServiceWorker();

// Wrap the app in StrictMode for better development experience
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>
);

// Mark when <PERSON>act has finished rendering
performanceMonitor.mark('app-rendered');
